<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🔧 Immediate API Key Debug</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #0f0f0f;
            color: #ffffff;
        }
        .container {
            background: #1a1a1a;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 8px 32px rgba(0, 0, 0, 0.3);
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-weight: 500;
        }
        .success { background: #1a4d3a; border-left: 4px solid #10b981; }
        .error { background: #4d1a1a; border-left: 4px solid #ef4444; }
        .warning { background: #4d3a1a; border-left: 4px solid #f59e0b; }
        .info { background: #1a3a4d; border-left: 4px solid #3b82f6; }
        .data-table {
            background: #2a2a2a;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 14px;
            white-space: pre-wrap;
            margin: 10px 0;
            border: 1px solid #404040;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 6px;
            cursor: pointer;
            font-size: 16px;
            margin: 10px 5px;
            transition: background 0.2s;
        }
        button:hover { background: #2563eb; }
        button:disabled { background: #6b7280; cursor: not-allowed; }
        .critical { 
            background: #7f1d1d; 
            border: 2px solid #dc2626; 
            animation: pulse 2s infinite;
        }
        @keyframes pulse {
            0%, 100% { opacity: 1; }
            50% { opacity: 0.8; }
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔧 Immediate API Key Debug</h1>
        <p>Let's immediately check why your FAQ generation is showing demo content.</p>
        
        <button onclick="testAPIKeyNow()" id="testBtn">🚀 Test API Key NOW</button>
        <button onclick="testSimpleGeneration()" id="simpleBtn">🧪 Test Simple Generation</button>
        
        <div id="results"></div>
    </div>

    <script>
        const supabase = window.supabase.createClient(
            'https://dlzshcshqjdghmtzlbma.supabase.co',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRsenNoY3NocWpkZ2htdHpsYm1hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExODUzODgsImV4cCI6MjA2Njc2MTM4OH0.EL4By0nom419JiorSHKiFckLqnh1sqmFvYnWTylB9Gk'
        );

        function updateResults(html) {
            document.getElementById('results').innerHTML = html;
        }

        async function testAPIKeyNow() {
            updateResults('<div class="status info">🔍 Testing API key configuration...</div>');
            
            try {
                // Test with simple text that should definitely work
                const { data, error } = await supabase.functions.invoke('analyze-content', {
                    body: {
                        text: "This is a simple test about artificial intelligence and machine learning technologies.",
                        type: 'text'
                    }
                });

                let html = '<div class="status info">📊 API Key Test Results:</div>';

                if (error) {
                    html += `
                        <div class="status error critical">
                            ❌ CRITICAL ERROR: ${error.message}
                        </div>
                        <div class="data-table">
Error Details: ${JSON.stringify(error, null, 2)}
                        </div>
                    `;
                } else {
                    // Check if it's demo content
                    const isDemoContent = data?.faqs?.some(faq => 
                        faq.question.includes("What is this content about?") ||
                        faq.answer.includes("This content discusses various topics") ||
                        faq.answer.includes("[DEMO MODE")
                    );

                    html += `
                        <div class="status ${isDemoContent ? 'error critical' : 'success'}">
                            ${isDemoContent ? '❌ DEMO MODE ACTIVE - API KEY ISSUE!' : '✅ API KEY WORKING - REAL AI ACTIVE'}
                        </div>
                        <div class="data-table">
Test Input: Simple AI text
FAQs Generated: ${data?.faqs?.length || 0}
Is Demo Mode: ${data?.isDemoMode || false}
Is Demo Content: ${isDemoContent}

First FAQ:
Q: ${data?.faqs?.[0]?.question || 'N/A'}
A: ${data?.faqs?.[0]?.answer?.substring(0, 300) || 'N/A'}...
                        </div>
                    `;

                    if (isDemoContent) {
                        html += `
                            <div class="status error critical">
                                <strong>🚨 CRITICAL ISSUE IDENTIFIED!</strong><br>
                                Your FAQ generation is in demo mode because:<br>
                                1. DEEPSEEK_API_KEY environment variable is missing/invalid<br>
                                2. The hardcoded fallback API key may be expired<br>
                                3. Edge function environment is not properly configured<br><br>
                                <strong>IMMEDIATE ACTION NEEDED:</strong><br>
                                The API key needs to be reconfigured in Supabase edge functions.
                            </div>
                        `;
                    }
                }

                updateResults(html);

            } catch (error) {
                updateResults(`
                    <div class="status error critical">
                        ❌ EXCEPTION: ${error.message}
                    </div>
                `);
            }
        }

        async function testSimpleGeneration() {
            updateResults('<div class="status info">🧪 Testing simple FAQ generation...</div>');
            
            try {
                const { data, error } = await supabase.functions.invoke('analyze-content', {
                    body: {
                        text: "FAQify is an AI-powered tool that helps businesses create professional FAQ sections for their websites. It uses advanced AI to analyze content and generate relevant questions and answers.",
                        type: 'text'
                    }
                });

                let html = '<div class="status info">📊 Simple Generation Test:</div>';

                if (error) {
                    html += `<div class="status error">❌ Error: ${error.message}</div>`;
                } else {
                    const isDemoContent = data?.faqs?.some(faq => 
                        faq.answer.includes("[DEMO MODE") ||
                        faq.question.includes("What is this content about?")
                    );

                    html += `
                        <div class="status ${isDemoContent ? 'error' : 'success'}">
                            ${isDemoContent ? '❌ Still Demo Mode' : '✅ Real AI Working'}
                        </div>
                        <div class="data-table">
Generated ${data?.faqs?.length || 0} FAQs:

${data?.faqs?.map((faq, i) => `${i+1}. Q: ${faq.question}
   A: ${faq.answer.substring(0, 200)}...`).join('\n\n') || 'No FAQs generated'}
                        </div>
                    `;
                }

                updateResults(html);

            } catch (error) {
                updateResults(`<div class="status error">❌ Exception: ${error.message}</div>`);
            }
        }

        // Auto-run the test when page loads
        window.onload = () => {
            setTimeout(testAPIKeyNow, 1000);
        };
    </script>
</body>
</html>

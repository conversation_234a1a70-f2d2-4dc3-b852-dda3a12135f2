<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Save Error - FAQify</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }
        .status.success { background: #dcfce7; color: #166534; border: 1px solid #bbf7d0; }
        .status.error { background: #fef2f2; color: #dc2626; border: 1px solid #fecaca; }
        .status.warning { background: #fefce8; color: #ca8a04; border: 1px solid #fef08a; }
        .status.info { background: #eff6ff; color: #2563eb; border: 1px solid #dbeafe; }
        .btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background 0.2s;
        }
        .btn:hover { background: #2563eb; }
        .data-table {
            background: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Debug Save Error</h1>
        <p>Test the exact database saving logic to identify the error.</p>
        
        <button class="btn" onclick="testSaveLogic()">🧪 Test Save Logic</button>
        
        <div id="test-results"></div>
    </div>

    <script>
        const supabaseUrl = 'https://dlzshcshqjdghmtzlbma.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRsenNoY3NocWpkZ2htdHpsYm1hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExODUzODgsImV4cCI6MjA2Njc2MTM4OH0.EL4By0nom419JiorSHKiFckLqnh1sqmFvYnWTylB9Gk';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        function updateResults(html) {
            document.getElementById('test-results').innerHTML = html;
        }

        async function testSaveLogic() {
            updateResults('<div class="status info">🧪 Testing save logic step by step...</div>');

            try {
                // Get current user
                const { data: { user }, error: userError } = await supabase.auth.getUser();
                if (userError || !user) {
                    updateResults('<div class="status error">❌ Please sign in first</div>');
                    return;
                }

                let html = `
                    <div class="container">
                        <h3>🧪 Save Logic Test</h3>
                        <div class="data-table">User ID: ${user.id}</div>
                `;

                // Step 1: Check profile
                html += '<h4>Step 1: Check Profile</h4>';
                const { data: profile, error: profileError } = await supabase
                    .from('profiles')
                    .select('id')
                    .eq('id', user.id)
                    .single();

                if (profileError) {
                    html += `<div class="status error">❌ Profile Error: ${profileError.message}</div>`;
                    
                    // Try to create profile
                    const { error: createProfileError } = await supabase
                        .from('profiles')
                        .insert({
                            id: user.id,
                            email: user.email || '',
                            full_name: user.user_metadata?.full_name || user.email || ''
                        });

                    if (createProfileError) {
                        html += `<div class="status error">❌ Create Profile Error: ${createProfileError.message}</div>`;
                    } else {
                        html += `<div class="status success">✅ Profile created successfully</div>`;
                    }
                } else {
                    html += `<div class="status success">✅ Profile exists</div>`;
                }

                // Step 2: Check subscription
                html += '<h4>Step 2: Check Subscription</h4>';
                const { data: subscription, error: subError } = await supabase
                    .from('user_subscriptions')
                    .select('*')
                    .eq('user_id', user.id)
                    .single();

                if (subError) {
                    html += `<div class="status error">❌ Subscription Error: ${subError.message}</div>`;
                    
                    // Try to create subscription
                    const { error: createSubError } = await supabase
                        .from('user_subscriptions')
                        .insert({
                            user_id: user.id,
                            plan_tier: 'Free',
                            faq_usage_limit: 5,
                            faq_usage_current: 0,
                            status: 'active',
                            last_reset_date: new Date().toISOString().split('T')[0]
                        });

                    if (createSubError) {
                        html += `<div class="status error">❌ Create Subscription Error: ${createSubError.message}</div>`;
                    } else {
                        html += `<div class="status success">✅ Subscription created successfully</div>`;
                    }
                } else {
                    html += `<div class="status success">✅ Subscription exists: ${subscription.plan_tier} (${subscription.faq_usage_current}/${subscription.faq_usage_limit})</div>`;
                }

                // Step 3: Test collection creation
                html += '<h4>Step 3: Test Collection Creation</h4>';
                const testTitle = `Test Collection ${Date.now()}`;
                
                const { data: collection, error: collectionError } = await supabase
                    .from('faq_collections')
                    .insert({
                        user_id: user.id,
                        title: testTitle,
                        source_url: 'https://test.com',
                        source_content: 'Test content',
                        status: 'published'
                    })
                    .select('id')
                    .single();

                if (collectionError) {
                    html += `<div class="status error">❌ Collection Error: ${collectionError.message}</div>`;
                    html += `<div class="data-table">Error Details: ${JSON.stringify(collectionError, null, 2)}</div>`;
                } else {
                    html += `<div class="status success">✅ Collection created: ${collection.id}</div>`;

                    // Step 4: Test FAQ insertion
                    html += '<h4>Step 4: Test FAQ Insertion</h4>';
                    const testFAQs = [
                        { question: 'Test Question 1?', answer: 'Test Answer 1' },
                        { question: 'Test Question 2?', answer: 'Test Answer 2' }
                    ];

                    const faqsToInsert = testFAQs.map((faq, index) => ({
                        collection_id: collection.id,
                        question: faq.question,
                        answer: faq.answer,
                        order_index: index
                    }));

                    const { error: faqsError } = await supabase
                        .from('faqs')
                        .insert(faqsToInsert);

                    if (faqsError) {
                        html += `<div class="status error">❌ FAQs Error: ${faqsError.message}</div>`;
                        html += `<div class="data-table">Error Details: ${JSON.stringify(faqsError, null, 2)}</div>`;
                    } else {
                        html += `<div class="status success">✅ FAQs inserted successfully</div>`;

                        // Step 5: Test usage increment
                        html += '<h4>Step 5: Test Usage Increment</h4>';
                        const { data: usageResult, error: usageError } = await supabase.rpc('increment_faq_usage_by_count', {
                            user_uuid: user.id,
                            faq_count: testFAQs.length
                        });

                        if (usageError) {
                            html += `<div class="status error">❌ Usage Error: ${usageError.message}</div>`;
                            html += `<div class="data-table">Error Details: ${JSON.stringify(usageError, null, 2)}</div>`;
                        } else {
                            html += `<div class="status success">✅ Usage incremented: ${usageResult}</div>`;
                        }

                        // Clean up test data
                        await supabase.from('faq_collections').delete().eq('id', collection.id);
                        html += `<div class="status info">🧹 Test data cleaned up</div>`;
                    }
                }

                html += '</div>';
                updateResults(html);

            } catch (error) {
                updateResults(`<div class="status error">❌ Exception: ${error.message}</div>`);
            }
        }

        // Auto-run test on page load
        window.addEventListener('load', () => {
            setTimeout(testSaveLogic, 1000);
        });
    </script>
</body>
</html>

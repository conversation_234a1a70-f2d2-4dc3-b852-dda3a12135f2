<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Assign Business <NAME_EMAIL></title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 15px;
            margin: 15px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 5px;
            cursor: pointer;
            margin: 10px 5px;
            font-size: 16px;
        }
        button:hover { background: #0056b3; }
        button:disabled { background: #6c757d; cursor: not-allowed; }
        input {
            width: 100%;
            padding: 10px;
            margin: 8px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
            font-size: 16px;
        }
        .plan-info {
            background: #e9ecef;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
        }
        .current-status {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 5px;
            margin: 15px 0;
            border-left: 4px solid #007bff;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Assign Business <NAME_EMAIL></h1>
        <p>This tool will upgrade the user to Business plan with 500 FAQ limit for testing purposes.</p>

        <!-- Configuration Section -->
        <div class="plan-info">
            <h3>📋 Configuration</h3>
            <div>
                <label>Supabase URL:</label>
                <input type="text" id="supabaseUrl" value="https://dlzshcshqjdghmtzlbma.supabase.co">
            </div>
            <div>
                <label>Supabase Service Role Key:</label>
                <input type="password" id="supabaseKey" placeholder="Enter service role key (required for admin operations)">
            </div>
            <button onclick="initializeSupabase()">Initialize Connection</button>
        </div>

        <!-- Current Status -->
        <div class="current-status" id="currentStatus">
            <h3>📊 Current Status</h3>
            <p>Click "Check Current Status" to see the current subscription details.</p>
        </div>

        <!-- Actions -->
        <div class="plan-info">
            <h3>🎯 Actions</h3>
            <button onclick="checkCurrentStatus()">Check Current Status</button>
            <button onclick="assignBusinessPlan()">Assign Business Plan</button>
            <button onclick="resetUsage()">Reset Usage Counter</button>
            <button onclick="verifyUpdate()">Verify Update</button>
        </div>

        <!-- Business Plan Details -->
        <div class="plan-info">
            <h3>💼 Business Plan Details</h3>
            <ul>
                <li><strong>Plan Tier:</strong> Business</li>
                <li><strong>FAQ Limit:</strong> 500 FAQs per month</li>
                <li><strong>Price:</strong> $29/month</li>
                <li><strong>Features:</strong> All features included</li>
                <li><strong>Status:</strong> Active</li>
                <li><strong>Duration:</strong> 1 month from activation</li>
            </ul>
        </div>

        <!-- Results -->
        <div id="results">
            <div class="info">Results will appear here...</div>
        </div>
    </div>

    <script>
        let supabase = null;
        const targetEmail = '<EMAIL>';

        function log(message, type = 'info') {
            const timestamp = new Date().toLocaleTimeString();
            const resultsDiv = document.getElementById('results');
            resultsDiv.innerHTML += `<div class="${type}">[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        function clearResults() {
            document.getElementById('results').innerHTML = '<div class="info">Results cleared...</div>';
        }

        async function initializeSupabase() {
            const url = document.getElementById('supabaseUrl').value;
            const key = document.getElementById('supabaseKey').value;

            if (!url || !key) {
                log('Please fill in both Supabase URL and Service Role Key', 'error');
                return;
            }

            try {
                supabase = window.supabase.createClient(url, key);
                log('✅ Supabase client initialized with service role key', 'success');
                
                // Test connection
                const { data, error } = await supabase.from('profiles').select('count').limit(1);
                if (error) {
                    log(`❌ Connection test failed: ${error.message}`, 'error');
                    return;
                }
                
                log('✅ Database connection verified', 'success');
                
            } catch (error) {
                log(`❌ Initialization failed: ${error.message}`, 'error');
            }
        }

        async function checkCurrentStatus() {
            if (!supabase) {
                log('❌ Please initialize Supabase connection first', 'error');
                return;
            }

            try {
                log(`🔍 Checking current status for ${targetEmail}...`, 'info');

                // Get user profile
                const { data: profile, error: profileError } = await supabase
                    .from('profiles')
                    .select('*')
                    .eq('email', targetEmail)
                    .single();

                if (profileError) {
                    log(`❌ Profile not found: ${profileError.message}`, 'error');
                    return;
                }

                log(`✅ Profile found: ${profile.full_name} (${profile.email})`, 'success');

                // Get subscription details
                const { data: subscription, error: subError } = await supabase
                    .from('user_subscriptions')
                    .select('*')
                    .eq('user_id', profile.id)
                    .single();

                if (subError) {
                    log(`❌ Subscription not found: ${subError.message}`, 'error');
                    return;
                }

                // Update current status display
                const statusDiv = document.getElementById('currentStatus');
                statusDiv.innerHTML = `
                    <h3>📊 Current Status for ${targetEmail}</h3>
                    <p><strong>User ID:</strong> ${profile.id}</p>
                    <p><strong>Plan Tier:</strong> ${subscription.plan_tier}</p>
                    <p><strong>Status:</strong> ${subscription.status}</p>
                    <p><strong>FAQ Usage:</strong> ${subscription.faq_usage_current}/${subscription.faq_usage_limit}</p>
                    <p><strong>Plan Activated:</strong> ${subscription.plan_activated_at || 'Not set'}</p>
                    <p><strong>Plan Expires:</strong> ${subscription.plan_expires_at || 'Not set'}</p>
                    <p><strong>Last Reset:</strong> ${subscription.last_reset_date || 'Not set'}</p>
                `;

                log(`📊 Current Plan: ${subscription.plan_tier} (${subscription.faq_usage_current}/${subscription.faq_usage_limit} FAQs used)`, 'info');

            } catch (error) {
                log(`❌ Status check failed: ${error.message}`, 'error');
            }
        }

        async function assignBusinessPlan() {
            if (!supabase) {
                log('❌ Please initialize Supabase connection first', 'error');
                return;
            }

            try {
                log(`🚀 Assigning Business plan to ${targetEmail}...`, 'info');

                // Get user profile first
                const { data: profile, error: profileError } = await supabase
                    .from('profiles')
                    .select('id, email')
                    .eq('email', targetEmail)
                    .single();

                if (profileError) {
                    log(`❌ Profile not found: ${profileError.message}`, 'error');
                    return;
                }

                // Calculate dates
                const now = new Date();
                const expiresAt = new Date(now.getTime() + (30 * 24 * 60 * 60 * 1000)); // 30 days from now

                // Update subscription
                const { data: updateData, error: updateError } = await supabase
                    .from('user_subscriptions')
                    .update({
                        plan_tier: 'Business',
                        faq_usage_limit: 500,
                        faq_usage_current: 0, // Reset usage
                        status: 'active',
                        plan_activated_at: now.toISOString(),
                        plan_expires_at: expiresAt.toISOString(),
                        plan_changed_at: now.toISOString(),
                        last_reset_date: now.toISOString().split('T')[0], // Today's date
                        updated_at: now.toISOString()
                    })
                    .eq('user_id', profile.id)
                    .select();

                if (updateError) {
                    log(`❌ Update failed: ${updateError.message}`, 'error');
                    return;
                }

                log('✅ Business plan assigned successfully!', 'success');
                log(`📅 Plan activated: ${now.toLocaleDateString()}`, 'success');
                log(`📅 Plan expires: ${expiresAt.toLocaleDateString()}`, 'success');
                log('🔄 Usage counter reset to 0/500', 'success');

                // Log the change for audit
                const { error: historyError } = await supabase
                    .from('subscription_history')
                    .insert({
                        user_id: profile.id,
                        from_plan_tier: 'Free', // Assuming upgrade from Free
                        to_plan_tier: 'Business',
                        change_type: 'upgrade',
                        change_reason: 'Manual assignment for testing',
                        effective_date: now.toISOString(),
                        new_expiration: expiresAt.toISOString(),
                        usage_at_change: 0,
                        metadata: {
                            assigned_by: 'admin_script',
                            purpose: 'testing',
                            timestamp: now.toISOString()
                        }
                    });

                if (historyError) {
                    log(`⚠️ History logging failed: ${historyError.message}`, 'warning');
                } else {
                    log('📝 Change logged to subscription history', 'info');
                }

            } catch (error) {
                log(`❌ Assignment failed: ${error.message}`, 'error');
            }
        }

        async function resetUsage() {
            if (!supabase) {
                log('❌ Please initialize Supabase connection first', 'error');
                return;
            }

            try {
                log(`🔄 Resetting usage counter for ${targetEmail}...`, 'info');

                // Get user profile
                const { data: profile, error: profileError } = await supabase
                    .from('profiles')
                    .select('id')
                    .eq('email', targetEmail)
                    .single();

                if (profileError) {
                    log(`❌ Profile not found: ${profileError.message}`, 'error');
                    return;
                }

                // Reset usage counter
                const { error: resetError } = await supabase
                    .from('user_subscriptions')
                    .update({
                        faq_usage_current: 0,
                        last_reset_date: new Date().toISOString().split('T')[0],
                        updated_at: new Date().toISOString()
                    })
                    .eq('user_id', profile.id);

                if (resetError) {
                    log(`❌ Reset failed: ${resetError.message}`, 'error');
                    return;
                }

                log('✅ Usage counter reset to 0', 'success');

            } catch (error) {
                log(`❌ Reset failed: ${error.message}`, 'error');
            }
        }

        async function verifyUpdate() {
            log('🔍 Verifying the update...', 'info');
            await checkCurrentStatus();
            
            // Additional verification
            if (supabase) {
                try {
                    const { data: profile } = await supabase
                        .from('profiles')
                        .select('id')
                        .eq('email', targetEmail)
                        .single();

                    const { data: subscription } = await supabase
                        .from('user_subscriptions')
                        .select('plan_tier, faq_usage_limit, faq_usage_current, status')
                        .eq('user_id', profile.id)
                        .single();

                    if (subscription.plan_tier === 'Business' && subscription.faq_usage_limit === 500) {
                        log('🎉 VERIFICATION SUCCESSFUL: Business plan is active with 500 FAQ limit!', 'success');
                    } else {
                        log('❌ VERIFICATION FAILED: Plan not updated correctly', 'error');
                    }

                } catch (error) {
                    log(`❌ Verification error: ${error.message}`, 'error');
                }
            }
        }
    </script>
</body>
</html>

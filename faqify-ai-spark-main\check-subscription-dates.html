<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Check Subscription Dates - FAQify</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 30px;
            box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 15px;
            border-radius: 8px;
            margin: 15px 0;
            font-weight: 500;
        }
        .status.success { background: #dcfce7; color: #166534; border: 1px solid #bbf7d0; }
        .status.error { background: #fef2f2; color: #dc2626; border: 1px solid #fecaca; }
        .status.warning { background: #fefce8; color: #ca8a04; border: 1px solid #fef08a; }
        .status.info { background: #eff6ff; color: #2563eb; border: 1px solid #dbeafe; }
        .btn {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 12px 24px;
            border-radius: 8px;
            cursor: pointer;
            font-size: 14px;
            margin: 5px;
            transition: background 0.2s;
        }
        .btn:hover { background: #2563eb; }
        .btn.success { background: #16a34a; }
        .btn.success:hover { background: #15803d; }
        .data-table {
            background: #f9fafb;
            padding: 15px;
            border-radius: 8px;
            margin: 10px 0;
            font-family: monospace;
            font-size: 12px;
            overflow-x: auto;
            white-space: pre-wrap;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Check Subscription Dates</h1>
        <p>This tool checks if the subscription date columns exist and have proper data.</p>
        
        <button class="btn" onclick="checkSubscriptionData()">🔍 Check Current Data</button>
        <button class="btn success" onclick="fixSubscriptionDates()">🔧 Fix Missing Dates</button>
        
        <div id="results"></div>
    </div>

    <script>
        const supabaseUrl = 'https://dlzshcshqjdghmtzlbma.supabase.co';
        const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRsenNoY3NocWpkZ2htdHpsYm1hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExODUzODgsImV4cCI6MjA2Njc2MTM4OH0.EL4By0nom419JiorSHKiFckLqnh1sqmFvYnWTylB9Gk';
        const supabase = window.supabase.createClient(supabaseUrl, supabaseKey);

        function updateResults(html) {
            document.getElementById('results').innerHTML = html;
        }

        async function checkSubscriptionData() {
            updateResults('<div class="status info">🔍 Checking subscription data...</div>');

            try {
                const { data: { user }, error: userError } = await supabase.auth.getUser();
                if (userError || !user) {
                    updateResults('<div class="status error">❌ Please sign in first</div>');
                    return;
                }

                let html = `
                    <div class="container">
                        <h3>🔍 Subscription Data Check</h3>
                        <div class="status info">User: ${user.email}</div>
                `;

                // Get current subscription data
                const { data: subscription, error: subError } = await supabase
                    .from('user_subscriptions')
                    .select('*')
                    .eq('user_id', user.id)
                    .single();

                if (subError) {
                    html += `<div class="status error">❌ Error fetching subscription: ${subError.message}</div>`;
                    updateResults(html + '</div>');
                    return;
                }

                if (!subscription) {
                    html += `<div class="status warning">⚠️ No subscription found for user</div>`;
                    updateResults(html + '</div>');
                    return;
                }

                // Check which fields exist and have data
                const fields = {
                    'plan_activated_at': subscription.plan_activated_at,
                    'plan_expires_at': subscription.plan_expires_at,
                    'last_reset_date': subscription.last_reset_date,
                    'plan_changed_at': subscription.plan_changed_at,
                    'previous_plan_tier': subscription.previous_plan_tier,
                    'created_at': subscription.created_at,
                    'updated_at': subscription.updated_at
                };

                html += `<h4>📋 Current Subscription Data:</h4>`;
                html += `<div class="data-table">`;
                html += `Plan Tier: ${subscription.plan_tier}\n`;
                html += `Status: ${subscription.status}\n`;
                html += `Usage: ${subscription.faq_usage_current}/${subscription.faq_usage_limit}\n\n`;

                html += `Date Fields:\n`;
                for (const [field, value] of Object.entries(fields)) {
                    const status = value ? '✅' : '❌';
                    const displayValue = value ? new Date(value).toLocaleString() : 'NULL/Missing';
                    html += `${status} ${field}: ${displayValue}\n`;
                }

                html += `</div>`;

                // Check if dates are missing
                const missingDates = !subscription.plan_activated_at || !subscription.plan_expires_at;
                
                if (missingDates) {
                    html += `
                        <div class="status warning">
                            ⚠️ Missing date fields detected!<br>
                            This is why the profile section shows "Not available".<br>
                            Click "Fix Missing Dates" to populate them.
                        </div>
                    `;
                } else {
                    html += `<div class="status success">✅ All date fields are properly populated!</div>`;
                }

                html += '</div>';
                updateResults(html);

            } catch (error) {
                updateResults(`<div class="status error">❌ Exception: ${error.message}</div>`);
            }
        }

        async function fixSubscriptionDates() {
            updateResults('<div class="status info">🔧 Fixing subscription dates...</div>');

            try {
                const { data: { user }, error: userError } = await supabase.auth.getUser();
                if (userError || !user) {
                    updateResults('<div class="status error">❌ Please sign in first</div>');
                    return;
                }

                let html = `
                    <div class="container">
                        <h3>🔧 Fixing Subscription Dates</h3>
                `;

                // Get current subscription
                const { data: subscription, error: subError } = await supabase
                    .from('user_subscriptions')
                    .select('*')
                    .eq('user_id', user.id)
                    .single();

                if (subError || !subscription) {
                    html += `<div class="status error">❌ Error fetching subscription: ${subError?.message || 'Not found'}</div>`;
                    updateResults(html + '</div>');
                    return;
                }

                // Prepare update data
                const now = new Date().toISOString();
                const updateData = {};

                // Set plan_activated_at if missing
                if (!subscription.plan_activated_at) {
                    updateData.plan_activated_at = subscription.created_at || now;
                    html += `<div class="status info">📅 Setting plan_activated_at to: ${updateData.plan_activated_at}</div>`;
                }

                // Set plan_expires_at if missing
                if (!subscription.plan_expires_at) {
                    if (subscription.plan_tier === 'Free') {
                        // Free plan never expires
                        updateData.plan_expires_at = new Date('2099-12-31').toISOString();
                    } else {
                        // Paid plans expire 1 month from activation
                        const activationDate = new Date(subscription.plan_activated_at || subscription.created_at || now);
                        const expirationDate = new Date(activationDate);
                        expirationDate.setMonth(expirationDate.getMonth() + 1);
                        updateData.plan_expires_at = expirationDate.toISOString();
                    }
                    html += `<div class="status info">📅 Setting plan_expires_at to: ${updateData.plan_expires_at}</div>`;
                }

                // Only set fields that exist in the subscription object
                if (subscription.hasOwnProperty('last_reset_date') && !subscription.last_reset_date) {
                    updateData.last_reset_date = new Date().toISOString().split('T')[0];
                    html += `<div class="status info">📅 Setting last_reset_date to: ${updateData.last_reset_date}</div>`;
                }

                if (subscription.hasOwnProperty('plan_changed_at') && !subscription.plan_changed_at) {
                    updateData.plan_changed_at = subscription.created_at || now;
                    html += `<div class="status info">📅 Setting plan_changed_at to: ${updateData.plan_changed_at}</div>`;
                }

                if (subscription.hasOwnProperty('previous_plan_tier') && !subscription.previous_plan_tier) {
                    updateData.previous_plan_tier = 'Free';
                    html += `<div class="status info">📅 Setting previous_plan_tier to: Free</div>`;
                }

                // Update the subscription if we have changes
                if (Object.keys(updateData).length > 0) {
                    updateData.updated_at = now;

                    const { error: updateError } = await supabase
                        .from('user_subscriptions')
                        .update(updateData)
                        .eq('user_id', user.id);

                    if (updateError) {
                        html += `<div class="status error">❌ Failed to update subscription: ${updateError.message}</div>`;
                    } else {
                        html += `
                            <div class="status success">
                                ✅ Subscription dates fixed successfully!<br>
                                Updated ${Object.keys(updateData).length} fields.<br>
                                The profile section should now show proper dates.
                            </div>
                        `;
                    }
                } else {
                    html += `<div class="status success">✅ All dates are already properly set!</div>`;
                }

                html += '</div>';
                updateResults(html);

            } catch (error) {
                updateResults(`<div class="status error">❌ Exception: ${error.message}</div>`);
            }
        }
    </script>
</body>
</html>

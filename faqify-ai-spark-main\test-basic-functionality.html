<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Test Basic FAQ Functionality</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
        }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 5px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover { background: #0056b3; }
        input, textarea {
            width: 100%;
            padding: 8px;
            margin: 5px 0;
            border: 1px solid #ddd;
            border-radius: 4px;
        }
        .results {
            margin-top: 20px;
            padding: 10px;
            background: #f8f9fa;
            border-radius: 5px;
            max-height: 400px;
            overflow-y: auto;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 Test Basic FAQ Functionality</h1>
        <p>Quick test to verify the FAQ generator is working.</p>

        <div>
            <label>Supabase URL:</label>
            <input type="text" id="supabaseUrl" placeholder="https://your-project.supabase.co">
        </div>
        <div>
            <label>Supabase Anon Key:</label>
            <input type="text" id="supabaseKey" placeholder="Your anon key">
        </div>
        <div>
            <label>Email:</label>
            <input type="email" id="userEmail" placeholder="<EMAIL>">
        </div>
        <div>
            <label>Password:</label>
            <input type="password" id="userPassword" placeholder="your-password">
        </div>
        <button onclick="initAndTest()">Initialize & Test</button>
        <button onclick="testWithText()">Test with Text Input</button>
        <button onclick="testSimpleUrl()">Test Simple URL</button>

        <div class="results" id="results">
            Results will appear here...
        </div>
    </div>

    <script>
        let supabase = null;

        function log(message, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `<div class="${type}">[${timestamp}] ${message}</div>`;
            resultsDiv.scrollTop = resultsDiv.scrollHeight;
        }

        async function initAndTest() {
            const url = document.getElementById('supabaseUrl').value;
            const key = document.getElementById('supabaseKey').value;
            const email = document.getElementById('userEmail').value;
            const password = document.getElementById('userPassword').value;

            if (!url || !key || !email || !password) {
                log('Please fill in all fields', 'error');
                return;
            }

            try {
                supabase = window.supabase.createClient(url, key);
                log('✅ Supabase client initialized', 'success');

                const { data, error } = await supabase.auth.signInWithPassword({
                    email: email,
                    password: password
                });

                if (error) {
                    log(`❌ Login failed: ${error.message}`, 'error');
                    return;
                }

                log(`✅ Logged in as ${data.user.email}`, 'success');
                
                // Test with simple text
                await testBasicFunctionality();

            } catch (error) {
                log(`❌ Error: ${error.message}`, 'error');
            }
        }

        async function testBasicFunctionality() {
            log('🧪 Testing basic FAQ generation with text...', 'info');

            const testText = `
                Shubhanshu Shukla, an Indian astronaut, recently returned to Earth after spending 20 days in space. 
                He was part of the Axiom-4 mission to the International Space Station (ISS). 
                The spacecraft carrying the crew splashed down in the Pacific Ocean off the coast of California. 
                This mission was significant for India's space program and the upcoming Gaganyaan mission.
                The astronauts completed 288 revolutions of Earth during their stay on the ISS.
            `;

            try {
                const { data, error } = await supabase.functions.invoke('analyze-content', {
                    body: {
                        type: 'text',
                        text: testText
                    }
                });

                if (error) {
                    log(`❌ Function error: ${error.message}`, 'error');
                    return;
                }

                if (data.error) {
                    log(`❌ Response error: ${data.message}`, 'error');
                    if (data.details) log(`Details: ${data.details}`, 'error');
                    return;
                }

                if (data.faqs && data.faqs.length > 0) {
                    log(`✅ SUCCESS: Generated ${data.faqs.length} FAQs from text`, 'success');
                    data.faqs.forEach((faq, index) => {
                        log(`Q${index + 1}: ${faq.question}`, 'info');
                        log(`A${index + 1}: ${faq.answer}`, 'info');
                    });
                } else {
                    log('❌ No FAQs generated', 'error');
                }

            } catch (error) {
                log(`❌ Test failed: ${error.message}`, 'error');
            }
        }

        async function testWithText() {
            if (!supabase) {
                log('❌ Please initialize first', 'error');
                return;
            }

            await testBasicFunctionality();
        }

        async function testSimpleUrl() {
            if (!supabase) {
                log('❌ Please initialize first', 'error');
                return;
            }

            log('🧪 Testing with simple URL...', 'info');

            try {
                const { data, error } = await supabase.functions.invoke('analyze-content', {
                    body: {
                        type: 'url',
                        url: 'https://example.com'
                    }
                });

                if (error) {
                    log(`❌ Function error: ${error.message}`, 'error');
                    return;
                }

                if (data.error) {
                    log(`⚠️ Expected error for example.com: ${data.message}`, 'info');
                    return;
                }

                log('📊 Response received', 'info');

            } catch (error) {
                log(`❌ Test failed: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>


import { Zap, Twitter, Linkedin, Github, Mail } from "lucide-react";
import { Link } from "react-router-dom";

export const Footer = () => {
  const footerLinks = {
    product: [
      { name: "Features", href: "#features" },
      { name: "Pricing", href: "#pricing" },
      { name: "How It Works", href: "#how-it-works" },
      { name: "Examples", href: "#examples" },
    ],
    company: [
      { name: "About Us", href: "/about" },
      { name: "Contact", href: "/contact" },
      { name: "Privacy Policy", href: "/privacy" },
      { name: "Terms of Service", href: "/terms" },
    ],

  };

  return (
    <footer className="bg-black border-t border-gray-800">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {/* Brand Section */}
          <div className="lg:col-span-2">
            <div className="flex items-center space-x-2 mb-4">
              <Zap className="h-8 w-8 text-blue-500" />
              <span className="text-2xl font-bold text-white">FAQify</span>
            </div>
            <p className="text-gray-400 mb-6 max-w-md">
              Transform your content into professional FAQ sections with AI-powered generation.
              Perfect for WordPress sites and beyond.
            </p>
            <div className="flex space-x-4">
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Twitter className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Linkedin className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Github className="h-5 w-5" />
              </a>
              <a href="#" className="text-gray-400 hover:text-white transition-colors">
                <Mail className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Product Links */}
          <div>
            <h3 className="text-white font-semibold mb-4">Product</h3>
            <ul className="space-y-2">
              {footerLinks.product.map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>

          {/* Company Links */}
          <div>
            <h3 className="text-white font-semibold mb-4">Company</h3>
            <ul className="space-y-2">
              {footerLinks.company.map((link) => (
                <li key={link.name}>
                  <a
                    href={link.href}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="text-gray-400 hover:text-white transition-colors"
                  >
                    {link.name}
                  </a>
                </li>
              ))}
            </ul>
          </div>
        </div>

        {/* Disclaimer Section */}
        <div className="border-t border-gray-800 mt-12 pt-8">
          <div className="bg-gray-900/30 rounded-lg p-6 mb-8">
            <h4 className="text-white font-semibold mb-3 flex items-center">
              <span className="text-yellow-500 mr-2">⚠️</span>
              Important Disclaimer
            </h4>
            <div className="text-gray-400 text-sm space-y-2">
              <p>
                <strong>AI-Generated Content:</strong> FAQs generated by our AI are suggestions based on content analysis.
                Always review and verify the accuracy of generated content before publishing on your website.
              </p>
              <p>
                <strong>Content Responsibility:</strong> You are responsible for ensuring that generated FAQs are accurate,
                appropriate, and comply with your industry regulations and legal requirements.
              </p>
              <p>
                <strong>No Warranty:</strong> While we strive for accuracy, we cannot guarantee that AI-generated content
                will be error-free or suitable for all purposes. Use generated content as a starting point for your own review and editing.
              </p>
              <p>
                <strong>Copyright & Legal:</strong> Ensure you have the right to use source content for FAQ generation.
                Generated FAQs should not infringe on third-party intellectual property rights.
              </p>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-gray-800 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center">
            <p className="text-gray-400 text-sm">
              © 2024 FAQify. All rights reserved.
            </p>
            <div className="flex items-center space-x-6 mt-4 md:mt-0">
              <span className="text-gray-400 text-sm">Made with ❤️ for WordPress users</span>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Quick FAQ Count Test</title>
    <script src="https://unpkg.com/@supabase/supabase-js@2"></script>
    <style>
        body { font-family: Arial, sans-serif; max-width: 800px; margin: 0 auto; padding: 20px; }
        .container { background: white; padding: 30px; border-radius: 10px; box-shadow: 0 2px 10px rgba(0,0,0,0.1); }
        .result { padding: 15px; margin: 15px 0; border-radius: 5px; font-weight: bold; }
        .success { background: #d4edda; color: #155724; }
        .error { background: #f8d7da; color: #721c24; }
        .info { background: #d1ecf1; color: #0c5460; }
        button { background: #007bff; color: white; border: none; padding: 12px 24px; border-radius: 5px; cursor: pointer; margin: 10px 5px; }
        input { width: 100%; padding: 10px; margin: 8px 0; border: 1px solid #ddd; border-radius: 4px; }
        pre { background: #f8f9fa; padding: 15px; border-radius: 5px; overflow-x: auto; font-size: 12px; }
    </style>
</head>
<body>
    <div class="container">
        <h1>🚀 Quick FAQ Count Test</h1>
        
        <div>
            <input type="text" id="supabaseUrl" placeholder="Supabase URL" value="https://dlzshcshqjdghmtzlbma.supabase.co">
            <input type="text" id="supabaseKey" placeholder="Supabase Anon Key">
            <input type="email" id="userEmail" placeholder="Email" value="<EMAIL>">
            <input type="password" id="userPassword" placeholder="Password">
            <button onclick="login()">Login</button>
        </div>

        <div>
            <h3>Test 8 FAQs (Your Problem Case)</h3>
            <button onclick="test8FAQs()">Test 8 FAQs</button>
            <button onclick="testWithRawCall()">Test with Raw API Call</button>
        </div>

        <div id="results"></div>
    </div>

    <script>
        let supabase = null;

        function log(message, type = 'info') {
            const div = document.createElement('div');
            div.className = `result ${type}`;
            div.innerHTML = `[${new Date().toLocaleTimeString()}] ${message}`;
            document.getElementById('results').appendChild(div);
        }

        async function login() {
            const url = document.getElementById('supabaseUrl').value;
            const key = document.getElementById('supabaseKey').value;
            const email = document.getElementById('userEmail').value;
            const password = document.getElementById('userPassword').value;

            supabase = window.supabase.createClient(url, key);
            
            const { data, error } = await supabase.auth.signInWithPassword({ email, password });
            
            if (error) {
                log(`❌ Login failed: ${error.message}`, 'error');
            } else {
                log(`✅ Logged in as ${email}`, 'success');
            }
        }

        async function test8FAQs() {
            if (!supabase) {
                log('❌ Please login first', 'error');
                return;
            }

            log('🧪 Testing 8 FAQ generation...', 'info');

            const testData = {
                type: 'text',
                text: 'FAQify is an AI-powered FAQ generation tool that helps businesses create comprehensive FAQ sections. It uses advanced natural language processing to analyze content and automatically generates relevant questions and answers. The tool supports multiple subscription plans and provides embeddable widgets for easy website integration. Users can customize their FAQ widgets and track performance through analytics.',
                faqCount: 8
            };

            console.log('📤 Sending request:', testData);

            try {
                const { data, error } = await supabase.functions.invoke('analyze-content', {
                    body: testData
                });

                console.log('📥 Received response:', data);

                if (error) {
                    log(`❌ Error: ${error.message}`, 'error');
                    return;
                }

                if (data.error) {
                    log(`❌ API Error: ${data.message}`, 'error');
                    return;
                }

                const actualCount = data.faqs ? data.faqs.length : 0;
                const requestedCount = 8;
                const success = actualCount === requestedCount;

                log(`📊 Result: Requested ${requestedCount}, Got ${actualCount} ${success ? '✅' : '❌'}`, success ? 'success' : 'error');
                
                if (data.requestedFaqCount) {
                    log(`🔍 Backend received: ${data.requestedFaqCount} FAQs`, 'info');
                }

                // Show detailed response
                const pre = document.createElement('pre');
                pre.textContent = JSON.stringify(data, null, 2);
                document.getElementById('results').appendChild(pre);

            } catch (error) {
                log(`❌ Test failed: ${error.message}`, 'error');
                console.error('Full error:', error);
            }
        }

        async function testWithRawCall() {
            if (!supabase) {
                log('❌ Please login first', 'error');
                return;
            }

            log('🔧 Testing with raw API call...', 'info');

            try {
                // Get the session token
                const { data: { session } } = await supabase.auth.getSession();
                
                if (!session) {
                    log('❌ No session found', 'error');
                    return;
                }

                const response = await fetch('https://dlzshcshqjdghmtzlbma.supabase.co/functions/v1/analyze-content', {
                    method: 'POST',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${session.access_token}`
                    },
                    body: JSON.stringify({
                        type: 'text',
                        text: 'FAQify is an AI-powered FAQ generation tool that helps businesses create comprehensive FAQ sections. It uses advanced natural language processing to analyze content and automatically generates relevant questions and answers.',
                        faqCount: 8
                    })
                });

                const data = await response.json();
                
                console.log('📥 Raw response:', data);

                const actualCount = data.faqs ? data.faqs.length : 0;
                log(`🔧 Raw API: Requested 8, Got ${actualCount} ${actualCount === 8 ? '✅' : '❌'}`, actualCount === 8 ? 'success' : 'error');

                // Show response
                const pre = document.createElement('pre');
                pre.textContent = JSON.stringify(data, null, 2);
                document.getElementById('results').appendChild(pre);

            } catch (error) {
                log(`❌ Raw test failed: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>

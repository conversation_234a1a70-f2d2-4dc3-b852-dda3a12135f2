<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Debug Widget API</title>
    <script src="https://cdn.jsdelivr.net/npm/@supabase/supabase-js@2"></script>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 12px 16px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: 500;
        }
        .status.info {
            background: #e3f2fd;
            color: #1976d2;
            border-left: 4px solid #2196f3;
        }
        .status.success {
            background: #e8f5e8;
            color: #2e7d32;
            border-left: 4px solid #4caf50;
        }
        .status.error {
            background: #ffebee;
            color: #c62828;
            border-left: 4px solid #f44336;
        }
        .code-block {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            overflow-x: auto;
            border-left: 4px solid #007bff;
            white-space: pre-wrap;
        }
        .btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .btn:hover {
            background: #0056b3;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🔍 Widget API Debug Tool</h1>
        <p>This tool helps debug the widget API issues.</p>
        
        <div class="status info">
            <strong>Collection ID:</strong> 5aee00a6-2195-493e-b3cf-8aef053c5e2b<br>
            <strong>API Endpoint:</strong> https://dlzshcshqjdghmtzlbma.supabase.co/functions/v1/get-faq-widget
        </div>
        
        <button class="btn" onclick="checkCollection()">1. Check if Collection Exists</button>
        <button class="btn" onclick="testWidgetAPI()">2. Test Widget API</button>
        <button class="btn" onclick="testWithAuth()">3. Test with Auth Headers</button>
        <button class="btn" onclick="listAllCollections()">4. List All Collections</button>
    </div>

    <div class="container">
        <h3>📊 Debug Results</h3>
        <div id="results">
            <div class="status info">Click the buttons above to run tests...</div>
        </div>
    </div>

    <script>
        const supabase = window.supabase.createClient(
            'https://dlzshcshqjdghmtzlbma.supabase.co',
            'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRsenNoY3NocWpkZ2htdHpsYm1hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExODUzODgsImV4cCI6MjA2Njc2MTM4OH0.EL4By0nom419JiorSHKiFckLqnh1sqmFvYnWTylB9Gk'
        );

        const collectionId = '5aee00a6-2195-493e-b3cf-8aef053c5e2b';

        function addResult(title, content, type = 'info') {
            const resultsDiv = document.getElementById('results');
            const timestamp = new Date().toLocaleTimeString();
            
            const resultDiv = document.createElement('div');
            resultDiv.innerHTML = `
                <div class="status ${type}">
                    <strong>[${timestamp}] ${title}</strong><br>
                    ${content}
                </div>
            `;
            
            resultsDiv.appendChild(resultDiv);
        }

        async function checkCollection() {
            addResult('🔍 Checking Collection', 'Querying database for collection...', 'info');
            
            try {
                const { data, error } = await supabase
                    .from('faq_collections')
                    .select(`
                        id,
                        title,
                        description,
                        status,
                        created_at,
                        faqs (
                            id,
                            question,
                            answer,
                            is_published
                        )
                    `)
                    .eq('id', collectionId)
                    .single();

                if (error) {
                    addResult('❌ Collection Check Failed', `Error: ${error.message}`, 'error');
                    return;
                }

                if (!data) {
                    addResult('❌ Collection Not Found', 'No collection found with this ID', 'error');
                    return;
                }

                const publishedFAQs = data.faqs?.filter(faq => faq.is_published) || [];
                
                addResult('✅ Collection Found', `
                    <strong>Title:</strong> ${data.title || 'No title'}<br>
                    <strong>Status:</strong> ${data.status}<br>
                    <strong>Total FAQs:</strong> ${data.faqs?.length || 0}<br>
                    <strong>Published FAQs:</strong> ${publishedFAQs.length}<br>
                    <strong>Created:</strong> ${new Date(data.created_at).toLocaleString()}
                `, 'success');

                if (publishedFAQs.length === 0) {
                    addResult('⚠️ No Published FAQs', 'Collection exists but has no published FAQs', 'error');
                }

            } catch (error) {
                addResult('❌ Database Error', `Error: ${error.message}`, 'error');
            }
        }

        async function testWidgetAPI() {
            addResult('🔍 Testing Widget API', 'Calling get-faq-widget endpoint...', 'info');
            
            try {
                const response = await fetch(`https://dlzshcshqjdghmtzlbma.supabase.co/functions/v1/get-faq-widget?collection_id=${collectionId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json'
                    }
                });

                const responseText = await response.text();
                
                addResult('📡 API Response', `
                    <strong>Status:</strong> ${response.status} ${response.statusText}<br>
                    <strong>Headers:</strong> ${JSON.stringify(Object.fromEntries(response.headers.entries()), null, 2)}<br>
                    <div class="code-block">${responseText}</div>
                `, response.ok ? 'success' : 'error');

                if (response.ok) {
                    try {
                        const data = JSON.parse(responseText);
                        addResult('✅ API Success', `
                            <strong>Collection:</strong> ${data.title || 'No title'}<br>
                            <strong>FAQs Count:</strong> ${data.faqs?.length || 0}
                        `, 'success');
                    } catch (parseError) {
                        addResult('⚠️ Parse Error', `Could not parse JSON response: ${parseError.message}`, 'error');
                    }
                }

            } catch (error) {
                addResult('❌ API Call Failed', `Network error: ${error.message}`, 'error');
            }
        }

        async function testWithAuth() {
            addResult('🔍 Testing with Auth Headers', 'Calling API with authorization...', 'info');
            
            try {
                const { data: { session } } = await supabase.auth.getSession();
                const token = session?.access_token;

                const response = await fetch(`https://dlzshcshqjdghmtzlbma.supabase.co/functions/v1/get-faq-widget?collection_id=${collectionId}`, {
                    method: 'GET',
                    headers: {
                        'Content-Type': 'application/json',
                        'Authorization': `Bearer ${token || 'no-token'}`,
                        'apikey': 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImRsenNoY3NocWpkZ2htdHpsYm1hIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTExODUzODgsImV4cCI6MjA2Njc2MTM4OH0.EL4By0nom419JiorSHKiFckLqnh1sqmFvYnWTylB9Gk'
                    }
                });

                const responseText = await response.text();
                
                addResult('📡 Auth API Response', `
                    <strong>Status:</strong> ${response.status} ${response.statusText}<br>
                    <strong>Token:</strong> ${token ? 'Present' : 'Missing'}<br>
                    <div class="code-block">${responseText}</div>
                `, response.ok ? 'success' : 'error');

            } catch (error) {
                addResult('❌ Auth API Failed', `Error: ${error.message}`, 'error');
            }
        }

        async function listAllCollections() {
            addResult('🔍 Listing All Collections', 'Fetching all published collections...', 'info');
            
            try {
                const { data, error } = await supabase
                    .from('faq_collections')
                    .select(`
                        id,
                        title,
                        status,
                        created_at,
                        faqs (id, is_published)
                    `)
                    .eq('status', 'published')
                    .order('created_at', { ascending: false });

                if (error) {
                    addResult('❌ List Collections Failed', `Error: ${error.message}`, 'error');
                    return;
                }

                if (!data || data.length === 0) {
                    addResult('⚠️ No Published Collections', 'No published collections found in database', 'error');
                    return;
                }

                const collectionsInfo = data.map(collection => {
                    const publishedFAQs = collection.faqs?.filter(faq => faq.is_published) || [];
                    return `
                        <strong>ID:</strong> ${collection.id}<br>
                        <strong>Title:</strong> ${collection.title || 'No title'}<br>
                        <strong>Published FAQs:</strong> ${publishedFAQs.length}<br>
                        <strong>Created:</strong> ${new Date(collection.created_at).toLocaleString()}<br>
                        <hr>
                    `;
                }).join('');

                addResult('✅ Published Collections', `
                    <strong>Total:</strong> ${data.length}<br><br>
                    ${collectionsInfo}
                `, 'success');

            } catch (error) {
                addResult('❌ List Error', `Error: ${error.message}`, 'error');
            }
        }
    </script>
</body>
</html>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>FAQify Widget Test</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 12px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        .test-section {
            margin-bottom: 40px;
            padding: 20px;
            border: 2px dashed #e0e0e0;
            border-radius: 8px;
        }
        .test-title {
            color: #333;
            margin-bottom: 15px;
            font-size: 18px;
            font-weight: 600;
        }
        .embed-code {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 6px;
            font-family: 'Courier New', monospace;
            font-size: 12px;
            margin-bottom: 20px;
            overflow-x: auto;
            border-left: 4px solid #007bff;
        }
        .status {
            padding: 10px 15px;
            border-radius: 6px;
            margin: 10px 0;
            font-weight: 500;
        }
        .status.info {
            background: #e3f2fd;
            color: #1976d2;
            border-left: 4px solid #2196f3;
        }
        .status.success {
            background: #e8f5e8;
            color: #2e7d32;
            border-left: 4px solid #4caf50;
        }
        .status.error {
            background: #ffebee;
            color: #c62828;
            border-left: 4px solid #f44336;
        }
        .widget-container {
            min-height: 100px;
            border: 1px solid #ddd;
            border-radius: 8px;
            padding: 20px;
            background: white;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>🧪 FAQify Widget Embed Test</h1>
        <p>This page tests the FAQify widget embed functionality to ensure it works correctly on external websites.</p>
        
        <div class="status info">
            <strong>Test Collection ID:</strong> 5aee00a6-2195-493e-b3cf-8aef053c5e2b<br>
            <strong>Widget Script URL:</strong> <span id="widget-url">Loading...</span><br>
            <strong>API URL:</strong> <span id="api-url">Loading...</span>
        </div>
    </div>

    <div class="container">
        <div class="test-section">
            <div class="test-title">🎯 Test 1: Auto-Initialize Widget (Data Attributes)</div>
            <div class="embed-code">
&lt;!-- FAQify Widget --&gt;<br>
&lt;div data-faqify-collection="5aee00a6-2195-493e-b3cf-8aef053c5e2b" data-faqify-theme="light"&gt;&lt;/div&gt;<br>
&lt;script src="https://faqify-ai-spark.netlify.app/widget.js"&gt;&lt;/script&gt;
            </div>
            
            <div class="widget-container">
                <!-- FAQify Widget -->
                <div data-faqify-collection="5aee00a6-2195-493e-b3cf-8aef053c5e2b" data-faqify-theme="light"></div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🎯 Test 2: Manual Initialize Widget (Dark Theme)</div>
            <div class="embed-code">
&lt;div id="faqify-widget-manual"&gt;&lt;/div&gt;<br>
&lt;script src="https://faqify-ai-spark.netlify.app/widget.js"&gt;&lt;/script&gt;<br>
&lt;script&gt;<br>
&nbsp;&nbsp;FAQify.init({<br>
&nbsp;&nbsp;&nbsp;&nbsp;collectionId: '5aee00a6-2195-493e-b3cf-8aef053c5e2b',<br>
&nbsp;&nbsp;&nbsp;&nbsp;theme: 'dark'<br>
&nbsp;&nbsp;});<br>
&lt;/script&gt;
            </div>
            
            <div class="widget-container">
                <div id="faqify-widget-manual"></div>
            </div>
        </div>

        <div class="test-section">
            <div class="test-title">🎯 Test 3: Blue Theme Widget</div>
            <div class="embed-code">
&lt;div data-faqify-collection="5aee00a6-2195-493e-b3cf-8aef053c5e2b" data-faqify-theme="blue"&gt;&lt;/div&gt;
            </div>
            
            <div class="widget-container">
                <div data-faqify-collection="5aee00a6-2195-493e-b3cf-8aef053c5e2b" data-faqify-theme="blue"></div>
            </div>
        </div>
    </div>

    <div class="container">
        <h3>📊 Test Results</h3>
        <div id="test-results">
            <div class="status info">⏳ Loading widget script...</div>
        </div>
    </div>

    <!-- Load the widget script -->
    <script src="https://faqify-ai-spark.netlify.app/widget.js"></script>
    
    <script>
        // Test script loading and functionality
        function updateResults(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `<div class="status ${type}">[${timestamp}] ${message}</div>`;
        }

        function updateInfo() {
            document.getElementById('widget-url').textContent = 'https://faqify-ai-spark.netlify.app/widget.js';
            document.getElementById('api-url').textContent = window.FAQify ? 'Widget loaded successfully' : 'Widget not loaded';
        }

        // Check if widget loaded
        window.addEventListener('load', function() {
            setTimeout(() => {
                updateInfo();
                
                if (typeof window.FAQify !== 'undefined') {
                    updateResults('✅ Widget script loaded successfully', 'success');
                    
                    // Test manual initialization
                    try {
                        window.FAQify.init({
                            collectionId: '5aee00a6-2195-493e-b3cf-8aef053c5e2b',
                            theme: 'dark'
                        });
                        updateResults('✅ Manual widget initialization successful', 'success');
                    } catch (error) {
                        updateResults(`❌ Manual initialization failed: ${error.message}`, 'error');
                    }
                    
                    // Check if widgets rendered
                    setTimeout(() => {
                        const widgets = document.querySelectorAll('.faqify-widget');
                        if (widgets.length > 0) {
                            updateResults(`✅ Found ${widgets.length} rendered widget(s)`, 'success');
                            
                            // Check for FAQ content
                            const faqItems = document.querySelectorAll('.faqify-faq-item');
                            if (faqItems.length > 0) {
                                updateResults(`✅ Found ${faqItems.length} FAQ item(s) loaded`, 'success');
                            } else {
                                updateResults('⚠️ Widgets rendered but no FAQ items found', 'error');
                            }
                        } else {
                            updateResults('❌ No widgets rendered', 'error');
                        }
                    }, 3000);
                    
                } else {
                    updateResults('❌ Widget script failed to load', 'error');
                }
            }, 1000);
        });

        // Monitor for errors
        window.addEventListener('error', function(e) {
            updateResults(`❌ JavaScript Error: ${e.message}`, 'error');
        });

        // Monitor console for widget messages
        const originalConsoleLog = console.log;
        const originalConsoleError = console.error;
        
        console.log = function(...args) {
            if (args[0] && args[0].includes && args[0].includes('FAQify')) {
                updateResults(`📝 Widget Log: ${args.join(' ')}`, 'info');
            }
            originalConsoleLog.apply(console, args);
        };
        
        console.error = function(...args) {
            if (args[0] && args[0].includes && args[0].includes('FAQify')) {
                updateResults(`❌ Widget Error: ${args.join(' ')}`, 'error');
            }
            originalConsoleError.apply(console, args);
        };
    </script>
</body>
</html>

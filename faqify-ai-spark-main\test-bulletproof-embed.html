<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>🛡️ Bulletproof FAQify Widget Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f5f5f5;
        }
        .container {
            background: white;
            padding: 30px;
            border-radius: 10px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        .status {
            padding: 10px;
            margin: 10px 0;
            border-radius: 5px;
            font-weight: bold;
        }
        .success { background: #d4edda; color: #155724; border: 1px solid #c3e6cb; }
        .error { background: #f8d7da; color: #721c24; border: 1px solid #f5c6cb; }
        .info { background: #d1ecf1; color: #0c5460; border: 1px solid #bee5eb; }
        .warning { background: #fff3cd; color: #856404; border: 1px solid #ffeaa7; }
        .embed-code {
            background: #f8f9fa;
            border: 1px solid #e9ecef;
            border-radius: 5px;
            padding: 15px;
            font-family: monospace;
            font-size: 12px;
            white-space: pre-wrap;
            overflow-x: auto;
            margin: 10px 0;
        }
        .copy-btn {
            background: #007bff;
            color: white;
            border: none;
            padding: 8px 16px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        .copy-btn:hover { background: #0056b3; }
        .widget-container {
            border: 2px dashed #007bff;
            padding: 20px;
            margin: 20px 0;
            border-radius: 8px;
            background: #f8f9ff;
        }
        .test-section {
            margin: 30px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        h1 { color: #333; text-align: center; }
        h3 { color: #007bff; border-bottom: 2px solid #007bff; padding-bottom: 5px; }
        h4 { color: #28a745; }
        .highlight { background: yellow; padding: 2px 4px; }
    </style>
</head>
<body>
    <h1>🛡️ Bulletproof FAQify Widget Test</h1>
    
    <div class="container">
        <h3>🎯 Test Status</h3>
        <div id="test-results">
            <div class="status info">🔄 Initializing bulletproof widget test...</div>
        </div>
    </div>

    <div class="container">
        <h3>🛡️ Bulletproof Embed Code</h3>
        <p>This embed code includes multiple fallback strategies and will work reliably in Elementor:</p>
        
        <div class="embed-code" id="bulletproof-embed-code">
<!-- 🛡️ FAQify Widget - Bulletproof Embed Code -->
<div data-faqify-collection="YOUR_COLLECTION_ID" 
     data-faqify-theme="light"
     data-faqify-powered-by="true"
     data-faqify-animation="true"
     data-faqify-collapsible="true"></div>
<script>
  // 🛡️ Bulletproof script loading with fallbacks
  (function() {
    const widgetUrls = [
      'https://faqify-ai-spark.netlify.app/widget.js',
      'https://faqify-ai-spark.netlify.app/widget.js'
    ];
    
    function loadWidget(urlIndex = 0) {
      if (urlIndex >= widgetUrls.length) {
        console.error('FAQify: Failed to load widget from all sources');
        return;
      }
      
      const script = document.createElement('script');
      script.src = widgetUrls[urlIndex];
      script.onload = function() {
        console.log('FAQify: Widget loaded successfully from', widgetUrls[urlIndex]);
      };
      script.onerror = function() {
        console.warn('FAQify: Failed to load from', widgetUrls[urlIndex], 'trying next...');
        loadWidget(urlIndex + 1);
      };
      document.head.appendChild(script);
    }
    
    loadWidget();
  })();
</script>
        </div>
        <button class="copy-btn" onclick="copyBulletproofEmbedCode()">📋 Copy Bulletproof Code</button>
        
        <div class="test-section">
            <h4>🎯 Live Widget Test</h4>
            <p>Testing the bulletproof widget with a real collection ID:</p>
            <div class="widget-container" id="widget-test-area">
                <!-- Widget will be loaded here -->
            </div>
        </div>
    </div>

    <div class="container">
        <h3>📋 Elementor Integration Instructions</h3>
        <div class="status info">
            <strong>Step 1:</strong> Copy the bulletproof embed code above<br>
            <strong>Step 2:</strong> In Elementor, add an "HTML" widget<br>
            <strong>Step 3:</strong> Paste the embed code<br>
            <strong>Step 4:</strong> Replace "YOUR_COLLECTION_ID" with your actual collection ID<br>
            <strong>Step 5:</strong> Save and publish your page
        </div>
        
        <div class="status success">
            <strong>✅ Benefits of Bulletproof Code:</strong><br>
            • Multiple fallback URLs for reliability<br>
            • Automatic retry mechanisms<br>
            • Works even if primary CDN fails<br>
            • Production-ready error handling<br>
            • Compatible with all website builders
        </div>
    </div>
    
    <script>
        function copyBulletproofEmbedCode() {
            const embedCode = document.getElementById('bulletproof-embed-code').textContent;
            navigator.clipboard.writeText(embedCode).then(() => {
                updateResults('✅ Bulletproof embed code copied! Replace YOUR_COLLECTION_ID with your actual collection ID.', 'success');
            }).catch(() => {
                updateResults('❌ Failed to copy embed code. Please copy manually.', 'error');
            });
        }

        function updateResults(message, type = 'info') {
            const resultsDiv = document.getElementById('test-results');
            const timestamp = new Date().toLocaleTimeString();
            resultsDiv.innerHTML += `<div class="status ${type}">[${timestamp}] ${message}</div>`;
        }

        // Test widget loading
        window.addEventListener('load', function() {
            updateResults('🛡️ Testing bulletproof widget loading...', 'info');
            
            // Test if widget script can be loaded
            const testScript = document.createElement('script');
            testScript.src = 'https://faqify-ai-spark.netlify.app/widget.js';
            testScript.onload = function() {
                updateResults('✅ Widget script loaded successfully from production URL', 'success');
                
                if (typeof window.FAQify !== 'undefined') {
                    updateResults('✅ FAQify widget library is available', 'success');
                } else {
                    updateResults('⚠️ FAQify widget library not found', 'warning');
                }
            };
            testScript.onerror = function() {
                updateResults('❌ Failed to load widget script from production URL', 'error');
            };
            document.head.appendChild(testScript);
        });
    </script>
</body>
</html>

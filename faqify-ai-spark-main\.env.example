# Supabase Configuration
VITE_SUPABASE_URL=your_supabase_project_url
VITE_SUPABASE_ANON_KEY=your_supabase_anon_key

# Supabase Service Role Key (for edge functions only)
SUPABASE_SERVICE_ROLE_KEY=your_supabase_service_role_key

# Google Gemini API (for FAQ generation)
GEMINI_API_KEY=your_google_gemini_api_key

# Stripe Configuration (Legacy - Optional)
STRIPE_SECRET_KEY=your_stripe_secret_key
STRIPE_WEBHOOK_SECRET=your_stripe_webhook_secret

# Razorpay Configuration (Primary Payment Gateway)
RAZORPAY_KEY_ID=your_razorpay_key_id
RAZORPAY_SECRET_KEY=your_razorpay_secret_key
RAZORPAY_WEBHOOK_SECRET=your_razorpay_webhook_secret

# Frontend Razorpay Configuration
VITE_RAZORPAY_KEY_ID=your_razorpay_key_id

# Optional: Payment Gateway Selection
VITE_PRIMARY_PAYMENT_GATEWAY=razorpay
VITE_ENABLE_STRIPE=false
VITE_ENABLE_RAZORPAY=true

# Optional: Multi-currency Support
VITE_DEFAULT_CURRENCY=usd
VITE_SUPPORTED_CURRENCIES=usd,inr,eur,gbp

# Optional: Location Detection
VITE_ENABLE_LOCATION_DETECTION=true

# Optional: Custom Domain for Widget CDN
WIDGET_CDN_URL=https://your-domain.com

# Development Settings
NODE_ENV=development
